/**
 * Validation utilities for API responses and data integrity
 */

import { OptionsChainData, OptionData, AIAnalysisResponse, StockSymbol } from '../types';

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Validate environment configuration
 */
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check Fyers configuration
  if (!import.meta.env.VITE_FYERS_APP_ID) {
    errors.push('VITE_FYERS_APP_ID is not configured');
  }
  
  if (!import.meta.env.VITE_FYERS_ACCESS_TOKEN) {
    errors.push('VITE_FYERS_ACCESS_TOKEN is not configured');
  }
  
  if (!import.meta.env.VITE_GEMINI_API_KEY) {
    errors.push('VITE_GEMINI_API_KEY is not configured');
  }
  
  // Check base URLs
  if (!import.meta.env.VITE_FYERS_BASE_URL) {
    errors.push('VITE_FYERS_BASE_URL is not configured');
  }
  
  if (!import.meta.env.VITE_GEMINI_BASE_URL) {
    errors.push('VITE_GEMINI_BASE_URL is not configured');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate stock symbol
 */
export function validateStockSymbol(symbol: StockSymbol): void {
  if (!symbol.symbol || symbol.symbol.trim() === '') {
    throw new ValidationError('Stock symbol is required', 'symbol');
  }
  
  if (!symbol.name || symbol.name.trim() === '') {
    throw new ValidationError('Stock name is required', 'name');
  }
  
  if (!symbol.lotSize || symbol.lotSize <= 0) {
    throw new ValidationError('Valid lot size is required', 'lotSize');
  }
  
  if (!symbol.tickSize || symbol.tickSize <= 0) {
    throw new ValidationError('Valid tick size is required', 'tickSize');
  }
}

/**
 * Validate option data
 */
export function validateOptionData(option: OptionData): void {
  if (!option.symbol || option.symbol.trim() === '') {
    throw new ValidationError('Option symbol is required', 'symbol');
  }
  
  if (!option.strikePrice || option.strikePrice <= 0) {
    throw new ValidationError('Valid strike price is required', 'strikePrice');
  }
  
  if (!['CE', 'PE'].includes(option.optionType)) {
    throw new ValidationError('Option type must be CE or PE', 'optionType');
  }
  
  if (!option.expiryDate || option.expiryDate.trim() === '') {
    throw new ValidationError('Expiry date is required', 'expiryDate');
  }
  
  if (option.ltp < 0) {
    throw new ValidationError('LTP cannot be negative', 'ltp');
  }
  
  if (option.volume < 0) {
    throw new ValidationError('Volume cannot be negative', 'volume');
  }
  
  if (option.openInterest < 0) {
    throw new ValidationError('Open Interest cannot be negative', 'openInterest');
  }
}

/**
 * Validate options chain data
 */
export function validateOptionsChainData(data: OptionsChainData): void {
  if (!data.underlyingSymbol || data.underlyingSymbol.trim() === '') {
    throw new ValidationError('Underlying symbol is required', 'underlyingSymbol');
  }
  
  if (!data.underlyingPrice || data.underlyingPrice <= 0) {
    throw new ValidationError('Valid underlying price is required', 'underlyingPrice');
  }
  
  if (!data.expiryDate || data.expiryDate.trim() === '') {
    throw new ValidationError('Expiry date is required', 'expiryDate');
  }
  
  if (!Array.isArray(data.callOptions)) {
    throw new ValidationError('Call options must be an array', 'callOptions');
  }
  
  if (!Array.isArray(data.putOptions)) {
    throw new ValidationError('Put options must be an array', 'putOptions');
  }
  
  if (data.callOptions.length === 0 && data.putOptions.length === 0) {
    throw new ValidationError('At least one option must be present', 'options');
  }
  
  // Validate individual options
  data.callOptions.forEach((option, index) => {
    try {
      validateOptionData(option);
    } catch (error) {
      throw new ValidationError(`Call option ${index}: ${error.message}`, `callOptions[${index}]`);
    }
  });
  
  data.putOptions.forEach((option, index) => {
    try {
      validateOptionData(option);
    } catch (error) {
      throw new ValidationError(`Put option ${index}: ${error.message}`, `putOptions[${index}]`);
    }
  });
}

/**
 * Validate AI analysis response
 */
export function validateAIAnalysisResponse(analysis: AIAnalysisResponse): void {
  if (!Array.isArray(analysis.recommendations)) {
    throw new ValidationError('Recommendations must be an array', 'recommendations');
  }
  
  if (!analysis.marketOutlook || analysis.marketOutlook.trim() === '') {
    throw new ValidationError('Market outlook is required', 'marketOutlook');
  }
  
  if (!analysis.riskAssessment || analysis.riskAssessment.trim() === '') {
    throw new ValidationError('Risk assessment is required', 'riskAssessment');
  }
  
  if (!Array.isArray(analysis.keyInsights)) {
    throw new ValidationError('Key insights must be an array', 'keyInsights');
  }
  
  if (!Array.isArray(analysis.warnings)) {
    throw new ValidationError('Warnings must be an array', 'warnings');
  }
  
  if (!analysis.timestamp || analysis.timestamp.trim() === '') {
    throw new ValidationError('Timestamp is required', 'timestamp');
  }
  
  // Validate each recommendation
  analysis.recommendations.forEach((rec, index) => {
    if (!rec.strikePrice || rec.strikePrice <= 0) {
      throw new ValidationError(`Recommendation ${index}: Valid strike price is required`, `recommendations[${index}].strikePrice`);
    }
    
    if (!['CE', 'PE'].includes(rec.optionType)) {
      throw new ValidationError(`Recommendation ${index}: Option type must be CE or PE`, `recommendations[${index}].optionType`);
    }
    
    if (rec.confidence < 0 || rec.confidence > 100) {
      throw new ValidationError(`Recommendation ${index}: Confidence must be between 0 and 100`, `recommendations[${index}].confidence`);
    }
    
    if (!rec.reasoning || rec.reasoning.trim() === '') {
      throw new ValidationError(`Recommendation ${index}: Reasoning is required`, `recommendations[${index}].reasoning`);
    }
    
    if (rec.entryPrice <= 0) {
      throw new ValidationError(`Recommendation ${index}: Valid entry price is required`, `recommendations[${index}].entryPrice`);
    }
    
    if (rec.targetPrice <= 0) {
      throw new ValidationError(`Recommendation ${index}: Valid target price is required`, `recommendations[${index}].targetPrice`);
    }
    
    if (rec.stopLoss <= 0) {
      throw new ValidationError(`Recommendation ${index}: Valid stop loss is required`, `recommendations[${index}].stopLoss`);
    }
  });
}

/**
 * Sanitize string input
 */
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

/**
 * Validate number range
 */
export function validateNumberRange(value: number, min: number, max: number, fieldName: string): void {
  if (value < min || value > max) {
    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);
  }
}

/**
 * Check if market is open (basic check)
 */
export function isMarketOpen(): boolean {
  const now = new Date();
  const day = now.getDay(); // 0 = Sunday, 6 = Saturday
  const hour = now.getHours();
  const minute = now.getMinutes();
  const timeInMinutes = hour * 60 + minute;
  
  // Market closed on weekends
  if (day === 0 || day === 6) {
    return false;
  }
  
  // Market hours: 9:15 AM to 3:30 PM (IST)
  const marketOpen = 9 * 60 + 15; // 9:15 AM
  const marketClose = 15 * 60 + 30; // 3:30 PM
  
  return timeInMinutes >= marketOpen && timeInMinutes <= marketClose;
}

/**
 * Format error message for display
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof ValidationError) {
    return `Validation Error: ${error.message}`;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
}
