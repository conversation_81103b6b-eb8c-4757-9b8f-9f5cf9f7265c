/* Global Styles for Options Chain Analyzer */
:root {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #213547;
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Table styles for better readability */
.MuiTableContainer-root {
  border-radius: 8px !important;
}

.MuiTableHead-root .MuiTableCell-root {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #dee2e6 !important;
}

.MuiTableRow-root:hover {
  background-color: rgba(25, 118, 210, 0.04) !important;
}

/* Card hover effects */
.MuiCard-root {
  transition: box-shadow 0.3s ease-in-out !important;
}

.MuiCard-root:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12) !important;
}

/* Button styles */
.MuiButton-root {
  font-weight: 500 !important;
  letter-spacing: 0.02857em !important;
}

/* Chip styles */
.MuiChip-root {
  font-weight: 500 !important;
}

/* Loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 2s infinite;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .MuiCard-root {
    margin-bottom: 16px !important;
  }

  .MuiTableContainer-root {
    font-size: 0.875rem !important;
  }
}

/* Print styles */
@media print {
  .MuiAppBar-root,
  .MuiButton-root,
  .no-print {
    display: none !important;
  }

  .MuiCard-root {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
