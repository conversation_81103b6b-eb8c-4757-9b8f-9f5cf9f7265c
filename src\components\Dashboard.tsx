import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Divider,
} from '@mui/material';
import { Refresh, Analytics, TrendingUp, Warning } from '@mui/icons-material';
import StockSelector from './StockSelector';
import OptionsChain from './OptionsChain';
import AIAnalysis from './AIAnalysis';
import LoadingSpinner from './LoadingSpinner';
import { AppState, StockSymbol, OptionsChainData, AIAnalysisResponse } from '../types';
import { fyersService } from '../services/fyersService';
import { geminiService } from '../services/geminiService';
import { UI_CONFIG } from '../utils/constants';

interface DashboardProps {
  appState: AppState;
  onStockSelect: (stock: StockSymbol) => void;
  onOptionsDataLoad: (data: OptionsChainData) => void;
  onAIAnalysisComplete: (analysis: AIAnalysisResponse) => void;
  onLoadingChange: (type: 'optionsData' | 'aiAnalysis', loading: boolean) => void;
  onError: (type: 'optionsData' | 'aiAnalysis', error: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({
  appState,
  onStockSelect,
  onOptionsDataLoad,
  onAIAnalysisComplete,
  onLoadingChange,
  onError,
}) => {
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);

  // Auto-refresh effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (autoRefresh && appState.selectedStock && !appState.loading.optionsData) {
      interval = setInterval(() => {
        handleRefreshData();
      }, UI_CONFIG.REFRESH_INTERVAL);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh, appState.selectedStock, appState.loading.optionsData]);

  // Load options data when stock is selected
  useEffect(() => {
    if (appState.selectedStock && !appState.optionsData) {
      handleLoadOptionsData();
    }
  }, [appState.selectedStock]);

  // Handle loading options data
  const handleLoadOptionsData = async () => {
    if (!appState.selectedStock) return;

    onLoadingChange('optionsData', true);
    
    try {
      const optionsData = await fyersService.getOptionsChain(appState.selectedStock.symbol);
      onOptionsDataLoad(optionsData);
      setLastRefresh(new Date());
    } catch (error) {
      onError('optionsData', error instanceof Error ? error.message : 'Failed to load options data');
    } finally {
      onLoadingChange('optionsData', false);
    }
  };

  // Handle AI analysis
  const handleAIAnalysis = async () => {
    if (!appState.optionsData) return;

    onLoadingChange('aiAnalysis', true);
    
    try {
      const analysis = await geminiService.getStrikeRecommendations(appState.optionsData);
      onAIAnalysisComplete(analysis);
    } catch (error) {
      onError('aiAnalysis', error instanceof Error ? error.message : 'AI analysis failed');
    } finally {
      onLoadingChange('aiAnalysis', false);
    }
  };

  // Handle refresh data
  const handleRefreshData = () => {
    if (appState.selectedStock) {
      handleLoadOptionsData();
    }
  };

  // Toggle auto-refresh
  const handleToggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  return (
    <Box>
      {/* Header Section */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Select Stock for Analysis
              </Typography>
              <StockSelector
                selectedStock={appState.selectedStock}
                onStockSelect={onStockSelect}
                disabled={appState.loading.optionsData}
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Controls
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Refresh />}
                  onClick={handleRefreshData}
                  disabled={!appState.selectedStock || appState.loading.optionsData}
                  fullWidth
                >
                  Refresh Data
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Analytics />}
                  onClick={handleAIAnalysis}
                  disabled={!appState.optionsData || appState.loading.aiAnalysis}
                  fullWidth
                >
                  {appState.loading.aiAnalysis ? 'Analyzing...' : 'AI Analysis'}
                </Button>
                
                <Button
                  variant={autoRefresh ? 'contained' : 'outlined'}
                  color={autoRefresh ? 'secondary' : 'primary'}
                  onClick={handleToggleAutoRefresh}
                  disabled={!appState.selectedStock}
                  fullWidth
                  size="small"
                >
                  Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
                </Button>
              </Box>
              
              {lastRefresh && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Status Section */}
      {appState.selectedStock && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    {appState.selectedStock.name} ({appState.selectedStock.symbol})
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      icon={<TrendingUp />}
                      label={`Lot Size: ${appState.selectedStock.lotSize}`}
                      color="primary"
                      variant="outlined"
                    />
                    {appState.optionsData && (
                      <Chip
                        label={`Spot: ₹${appState.optionsData.underlyingPrice.toFixed(2)}`}
                        color="success"
                      />
                    )}
                  </Box>
                </Box>
                
                {/* Loading Progress */}
                {(appState.loading.optionsData || appState.loading.aiAnalysis) && (
                  <Box sx={{ mb: 2 }}>
                    <LinearProgress />
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                      {appState.loading.optionsData && 'Loading options data...'}
                      {appState.loading.aiAnalysis && 'AI analysis in progress...'}
                    </Typography>
                  </Box>
                )}
                
                {/* Error Messages */}
                {(appState.error.optionsData || appState.error.aiAnalysis) && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {appState.error.optionsData || appState.error.aiAnalysis}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Options Chain */}
        <Grid item xs={12} lg={8}>
          {appState.loading.optionsData ? (
            <LoadingSpinner message="Loading options chain data..." />
          ) : appState.optionsData ? (
            <OptionsChain 
              data={appState.optionsData}
              selectedStock={appState.selectedStock}
            />
          ) : appState.selectedStock ? (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Options Chain
                </Typography>
                <Alert severity="info">
                  Click "Refresh Data" to load options chain for {appState.selectedStock.symbol}
                </Alert>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Options Chain
                </Typography>
                <Alert severity="info">
                  Select a stock to view its options chain
                </Alert>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* AI Analysis */}
        <Grid item xs={12} lg={4}>
          {appState.loading.aiAnalysis ? (
            <LoadingSpinner message="AI analysis in progress..." />
          ) : appState.aiAnalysis ? (
            <AIAnalysis 
              analysis={appState.aiAnalysis}
              optionsData={appState.optionsData}
            />
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  AI Analysis
                </Typography>
                <Alert severity="info" icon={<Analytics />}>
                  Load options data and click "AI Analysis" to get strike recommendations
                </Alert>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
