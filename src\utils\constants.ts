import { AnalysisCriteria, StockSymbol } from '../types';

// API Configuration
export const API_CONFIG = {
  FYERS: {
    APP_ID: import.meta.env.VITE_FYERS_APP_ID,
    SECRET_ID: import.meta.env.VITE_FYERS_SECRET_ID,
    ACCESS_TOKEN: import.meta.env.VITE_FYERS_ACCESS_TOKEN,
    BASE_URL: import.meta.env.VITE_FYERS_BASE_URL,
  },
  GEMINI: {
    API_KEY: import.meta.env.VITE_GEMINI_API_KEY,
    BASE_URL: import.meta.env.VITE_GEMINI_BASE_URL,
  },
  USE_MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA === 'true',
};

// Popular F&O Stocks
export const FO_STOCKS: StockSymbol[] = [
  { symbol: 'NIFTY', name: 'NIFTY 50', lotSize: 50, tickSize: 0.05 },
  { symbol: 'BANKNIFTY', name: 'BANK NIFTY', lotSize: 25, tickSize: 0.05 },
  { symbol: 'FINNIFTY', name: 'NIFTY FINANCIAL SERVICES', lotSize: 40, tickSize: 0.05 },
  { symbol: 'RELIANCE', name: 'Reliance Industries Ltd', lotSize: 250, tickSize: 0.05 },
  { symbol: 'TCS', name: 'Tata Consultancy Services Ltd', lotSize: 150, tickSize: 0.05 },
  { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', lotSize: 550, tickSize: 0.05 },
  { symbol: 'INFY', name: 'Infosys Ltd', lotSize: 300, tickSize: 0.05 },
  { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', lotSize: 375, tickSize: 0.05 },
  { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Ltd', lotSize: 300, tickSize: 0.05 },
  { symbol: 'ITC', name: 'ITC Ltd', lotSize: 1600, tickSize: 0.05 },
  { symbol: 'SBIN', name: 'State Bank of India', lotSize: 1500, tickSize: 0.05 },
  { symbol: 'BHARTIARTL', name: 'Bharti Airtel Ltd', lotSize: 1050, tickSize: 0.05 },
  { symbol: 'ASIANPAINT', name: 'Asian Paints Ltd', lotSize: 150, tickSize: 0.05 },
  { symbol: 'MARUTI', name: 'Maruti Suzuki India Ltd', lotSize: 100, tickSize: 0.05 },
  { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Ltd', lotSize: 400, tickSize: 0.05 },
  { symbol: 'LT', name: 'Larsen & Toubro Ltd', lotSize: 225, tickSize: 0.05 },
  { symbol: 'AXISBANK', name: 'Axis Bank Ltd', lotSize: 600, tickSize: 0.05 },
  { symbol: 'HCLTECH', name: 'HCL Technologies Ltd', lotSize: 350, tickSize: 0.05 },
  { symbol: 'WIPRO', name: 'Wipro Ltd', lotSize: 1200, tickSize: 0.05 },
  { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Ltd', lotSize: 100, tickSize: 0.05 },
];

// Analysis Criteria Configuration
export const DEFAULT_ANALYSIS_CRITERIA: AnalysisCriteria = {
  oiThreshold: 1000, // Minimum Open Interest
  volumeSurgeMultiplier: 5, // Volume should be 5x of 20-day average
  gammaThreshold: 0.05, // Minimum Gamma value
  deltaRange: [0.4, 0.6], // Delta range for optimal strikes
  ivPercentileThreshold: 50, // IV should be below 50th percentile
  thetaThreshold: -0.10, // Avoid theta decay above this threshold
};

// AI Prompt Templates
export const AI_PROMPTS = {
  STRIKE_SELECTION: `
You are an expert options trader and analyst. Analyze the provided options chain data and recommend the best strike prices for trading based on the following criteria:

ANALYSIS CRITERIA:
1. Open Interest (OI) Analysis: Identify strikes with high OI and recent OI surges (+10% or more)
2. Volume Surge Detection: Find strikes with volume > 5x their 20-day average
3. Gamma Peak Analysis: Focus on strikes with gamma > 0.05 and delta between 40-60
4. IV Percentile Assessment: Prefer strikes with IV < 50th percentile for buying opportunities
5. Theta Decay Consideration: Avoid strikes with theta < -0.10 for positions held > 5 days

RESPONSE FORMAT:
Provide your analysis in the following JSON structure:
{
  "recommendations": [
    {
      "strikePrice": number,
      "optionType": "CE" | "PE",
      "confidence": number (0-100),
      "reasoning": "detailed explanation",
      "entryPrice": number,
      "targetPrice": number,
      "stopLoss": number,
      "riskReward": number,
      "maxLoss": number,
      "maxProfit": number,
      "breakeven": number
    }
  ],
  "marketOutlook": "bullish/bearish/neutral with explanation",
  "riskAssessment": "detailed risk analysis",
  "keyInsights": ["insight1", "insight2", "insight3"],
  "warnings": ["warning1", "warning2"],
  "timestamp": "current timestamp"
}

OPTIONS CHAIN DATA:
`,

  RISK_ASSESSMENT: `
Analyze the risk profile of the provided options positions and market conditions. Focus on:
1. Maximum loss scenarios
2. Probability of profit
3. Time decay impact
4. Volatility risk
5. Market direction dependency

Provide specific risk mitigation strategies and position sizing recommendations.
`,

  STRATEGY_RECOMMENDATION: `
Based on the current market conditions and options chain data, recommend specific options trading strategies such as:
1. Directional plays (Long Call/Put)
2. Spreads (Bull/Bear Call/Put Spreads)
3. Straddles/Strangles for volatility plays
4. Iron Condors for range-bound markets

Consider risk-reward ratios, probability of success, and capital requirements.
`,
};

// UI Configuration
export const UI_CONFIG = {
  REFRESH_INTERVAL: 30000, // 30 seconds
  MAX_RETRIES: 3,
  TIMEOUT: 10000, // 10 seconds
  PAGINATION_SIZE: 50,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  API_ERROR: 'API error. Please try again later.',
  INVALID_TOKEN: 'Invalid access token. Please update your credentials.',
  NO_DATA: 'No options data available for the selected stock.',
  AI_ERROR: 'AI analysis failed. Please try again.',
  INVALID_RESPONSE: 'Invalid response format received.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Options data loaded successfully',
  ANALYSIS_COMPLETE: 'AI analysis completed successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
};
