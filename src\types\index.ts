// Fyers API Types
export interface FyersAuthConfig {
  appId: string;
  secretId: string;
  accessToken: string;
  baseUrl: string;
}

export interface OptionData {
  symbol: string;
  strikePrice: number;
  optionType: 'CE' | 'PE';
  expiryDate: string;
  ltp: number;
  bid: number;
  ask: number;
  volume: number;
  openInterest: number;
  impliedVolatility: number;
  delta: number;
  gamma: number;
  theta: number;
  vega: number;
  rho: number;
  change: number;
  changePercent: number;
}

export interface OptionsChainData {
  underlyingSymbol: string;
  underlyingPrice: number;
  expiryDate: string;
  callOptions: OptionData[];
  putOptions: OptionData[];
  timestamp: string;
}

export interface StockSymbol {
  symbol: string;
  name: string;
  lotSize: number;
  tickSize: number;
}

// AI Analysis Types
export interface AIAnalysisRequest {
  optionsData: OptionsChainData;
  analysisType: 'STRIKE_SELECTION' | 'RISK_ASSESSMENT' | 'STRATEGY_RECOMMENDATION';
  userPreferences?: {
    riskTolerance: 'LOW' | 'MEDIUM' | 'HIGH';
    timeHorizon: number; // days
    maxLoss: number;
    targetProfit: number;
  };
}

export interface StrikeRecommendation {
  strikePrice: number;
  optionType: 'CE' | 'PE';
  confidence: number;
  reasoning: string;
  entryPrice: number;
  targetPrice: number;
  stopLoss: number;
  riskReward: number;
  maxLoss: number;
  maxProfit: number;
  breakeven: number;
}

export interface AIAnalysisResponse {
  recommendations: StrikeRecommendation[];
  marketOutlook: string;
  riskAssessment: string;
  keyInsights: string[];
  warnings: string[];
  timestamp: string;
}

// UI State Types
export interface AppState {
  selectedStock: StockSymbol | null;
  optionsData: OptionsChainData | null;
  aiAnalysis: AIAnalysisResponse | null;
  loading: {
    optionsData: boolean;
    aiAnalysis: boolean;
  };
  error: {
    optionsData: string | null;
    aiAnalysis: string | null;
  };
}

// API Response Types
export interface FyersOptionsChainResponse {
  s: string; // status
  code: number;
  message: string;
  data?: {
    optionsChain: any[];
    underlying: {
      symbol: string;
      ltp: number;
    };
  };
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

// Analysis Criteria Types
export interface AnalysisCriteria {
  oiThreshold: number;
  volumeSurgeMultiplier: number;
  gammaThreshold: number;
  deltaRange: [number, number];
  ivPercentileThreshold: number;
  thetaThreshold: number;
}
