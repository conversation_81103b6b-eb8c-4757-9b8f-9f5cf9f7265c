import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  FyersAuthConfig,
  OptionsChainData,
  OptionData,
  FyersOptionsChainResponse,
  StockSymbol
} from '../types';
import { API_CONFIG, ERROR_MESSAGES, UI_CONFIG } from '../utils/constants';

class FyersService {
  private api: AxiosInstance;
  private config: FyersAuthConfig;

  constructor() {
    // Validate required configuration
    if (!API_CONFIG.FYERS.APP_ID || !API_CONFIG.FYERS.ACCESS_TOKEN) {
      throw new Error('Fyers API configuration is incomplete. Please check your .env file.');
    }

    this.config = {
      appId: API_CONFIG.FYERS.APP_ID,
      secretId: API_CONFIG.FYERS.SECRET_ID,
      accessToken: API_CONFIG.FYERS.ACCESS_TOKEN,
      baseUrl: API_CONFIG.FYERS.BASE_URL,
    };

    this.api = axios.create({
      baseURL: this.config.baseUrl,
      timeout: UI_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${this.config.appId}:${this.config.accessToken}`,
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log('Fyers API Request:', config.url);
        return config;
      },
      (error) => {
        console.error('Fyers API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log('Fyers API Response:', response.data);
        return response;
      },
      (error) => {
        console.error('Fyers API Response Error:', error);
        if (error.response?.status === 401) {
          throw new Error(ERROR_MESSAGES.INVALID_TOKEN);
        }
        if (!error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw new Error(ERROR_MESSAGES.API_ERROR);
      }
    );
  }

  /**
   * Update access token
   */
  updateAccessToken(token: string): void {
    this.config.accessToken = token;
    this.api.defaults.headers['Authorization'] = `${this.config.appId}:${token}`;
  }

  /**
   * Get user profile to verify authentication
   */
  async getProfile(): Promise<any> {
    try {
      const response: AxiosResponse = await this.api.get('/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  /**
   * Get options chain data for a symbol
   * Updated for Fyers API v3
   */
  async getOptionsChain(symbol: string, expiryDate?: string): Promise<OptionsChainData> {
    try {
      // Format symbol for Fyers API (e.g., NSE:NIFTY50-INDEX, NSE:RELIANCE-EQ)
      const formattedSymbol = this.formatSymbol(symbol);

      // First get the current price
      const currentPrice = await this.getQuote(symbol);

      // Get options chain - Updated endpoint for v3
      const response: AxiosResponse = await this.api.get('/optchain', {
        params: {
          symbol: formattedSymbol,
          ...(expiryDate && { expiry: expiryDate })
        },
      });

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(response.data.message || ERROR_MESSAGES.NO_DATA);
      }

      return this.transformOptionsChainData(response.data.d, symbol, currentPrice);
    } catch (error) {
      console.error('Error fetching options chain:', error);
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Invalid access token. Please regenerate your Fyers access token.');
        }
        if (error.response?.status === 403) {
          throw new Error('Access forbidden. Please check your API permissions.');
        }
      }
      throw error;
    }
  }

  /**
   * Get current market price for a symbol
   * Updated for Fyers API v3
   */
  async getQuote(symbol: string): Promise<number> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/quotes', {
        params: { symbols: formattedSymbol },
      });

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(ERROR_MESSAGES.NO_DATA);
      }

      // Updated response structure for v3
      const quoteData = response.data.d[formattedSymbol];
      return quoteData?.v?.lp || quoteData?.lp || 0;
    } catch (error) {
      console.error('Error fetching quote:', error);
      throw error;
    }
  }

  /**
   * Get historical data for analysis
   */
  async getHistoricalData(
    symbol: string, 
    resolution: string = '1D', 
    fromDate: string, 
    toDate: string
  ): Promise<any> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/history', {
        params: {
          symbol: formattedSymbol,
          resolution,
          date_format: '1',
          range_from: fromDate,
          range_to: toDate,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }

  /**
   * Format symbol for Fyers API v3
   */
  private formatSymbol(symbol: string): string {
    // Handle index symbols - Updated for v3
    if (symbol === 'NIFTY') {
      return 'NSE:NIFTY50-INDEX';
    }
    if (symbol === 'BANKNIFTY') {
      return 'NSE:BANKNIFTY-INDEX';
    }
    if (symbol === 'FINNIFTY') {
      return 'NSE:FINNIFTY-INDEX';
    }

    // Handle equity symbols
    return `NSE:${symbol}-EQ`;
  }

  /**
   * Transform Fyers API response to our data structure
   * Updated for v3 response format
   */
  private transformOptionsChainData(data: any, symbol: string, spotPrice: number): OptionsChainData {
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];

    // Process options chain data - Updated for v3 format
    if (data && typeof data === 'object') {
      // v3 API returns data in a different structure
      Object.keys(data).forEach((key) => {
        const option = data[key];
        if (option && typeof option === 'object') {
          const optionData: OptionData = {
            symbol: option.fyToken || key,
            strikePrice: option.strikePrice || 0,
            optionType: option.optionType === 'CE' ? 'CE' : 'PE',
            expiryDate: option.expiry || '',
            ltp: option.ltp || 0,
            bid: option.bid || 0,
            ask: option.ask || 0,
            volume: option.volume || 0,
            openInterest: option.oi || 0,
            impliedVolatility: option.iv || 0,
            delta: option.delta || 0,
            gamma: option.gamma || 0,
            theta: option.theta || 0,
            vega: option.vega || 0,
            rho: option.rho || 0,
            change: option.ch || 0,
            changePercent: option.chp || 0,
          };

          if (option.optionType === 'CE') {
            callOptions.push(optionData);
          } else if (option.optionType === 'PE') {
            putOptions.push(optionData);
          }
        }
      });
    }

    // Sort by strike price
    callOptions.sort((a, b) => a.strikePrice - b.strikePrice);
    putOptions.sort((a, b) => a.strikePrice - b.strikePrice);

    return {
      underlyingSymbol: symbol,
      underlyingPrice: spotPrice,
      expiryDate: callOptions[0]?.expiryDate || putOptions[0]?.expiryDate || '',
      callOptions,
      putOptions,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get available expiry dates for a symbol
   */
  async getExpiryDates(symbol: string): Promise<string[]> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/optchain', {
        params: { symbol: formattedSymbol, get_expiry: 1 },
      });

      if (response.data.s !== 'ok' || !response.data.data) {
        return [];
      }

      return response.data.data.expiry_dates || [];
    } catch (error) {
      console.error('Error fetching expiry dates:', error);
      return [];
    }
  }
}

// Export singleton instance
export const fyersService = new FyersService();
export default fyersService;
