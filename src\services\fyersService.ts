import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  FyersAuthConfig, 
  OptionsChainData, 
  OptionData, 
  FyersOptionsChainResponse,
  StockSymbol 
} from '../types';
import { API_CONFIG, ERROR_MESSAGES, UI_CONFIG } from '../utils/constants';

class FyersService {
  private api: AxiosInstance;
  private config: FyersAuthConfig;

  constructor() {
    this.config = {
      appId: API_CONFIG.FYERS.APP_ID,
      secretId: API_CONFIG.FYERS.SECRET_ID,
      accessToken: API_CONFIG.FYERS.ACCESS_TOKEN,
      baseUrl: API_CONFIG.FYERS.BASE_URL,
    };

    this.api = axios.create({
      baseURL: this.config.baseUrl,
      timeout: UI_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${this.config.appId}:${this.config.accessToken}`,
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log('Fyers API Request:', config.url);
        return config;
      },
      (error) => {
        console.error('Fyers API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log('Fyers API Response:', response.data);
        return response;
      },
      (error) => {
        console.error('Fyers API Response Error:', error);
        if (error.response?.status === 401) {
          throw new Error(ERROR_MESSAGES.INVALID_TOKEN);
        }
        if (!error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw new Error(ERROR_MESSAGES.API_ERROR);
      }
    );
  }

  /**
   * Update access token
   */
  updateAccessToken(token: string): void {
    this.config.accessToken = token;
    this.api.defaults.headers['Authorization'] = `${this.config.appId}:${token}`;
  }

  /**
   * Get user profile to verify authentication
   */
  async getProfile(): Promise<any> {
    try {
      const response: AxiosResponse = await this.api.get('/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  /**
   * Get options chain data for a symbol
   */
  async getOptionsChain(symbol: string, expiryDate?: string): Promise<OptionsChainData> {
    try {
      // Format symbol for Fyers API (e.g., NSE:NIFTY-INDEX, NSE:RELIANCE-EQ)
      const formattedSymbol = this.formatSymbol(symbol);
      
      const params: any = {
        symbol: formattedSymbol,
      };

      if (expiryDate) {
        params.expiry = expiryDate;
      }

      const response: AxiosResponse<FyersOptionsChainResponse> = await this.api.get('/optchain', {
        params,
      });

      if (response.data.s !== 'ok' || !response.data.data) {
        throw new Error(response.data.message || ERROR_MESSAGES.NO_DATA);
      }

      return this.transformOptionsChainData(response.data.data, symbol);
    } catch (error) {
      console.error('Error fetching options chain:', error);
      throw error;
    }
  }

  /**
   * Get current market price for a symbol
   */
  async getQuote(symbol: string): Promise<number> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/quotes', {
        params: { symbols: formattedSymbol },
      });

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(ERROR_MESSAGES.NO_DATA);
      }

      return response.data.d[0]?.v?.lp || 0;
    } catch (error) {
      console.error('Error fetching quote:', error);
      throw error;
    }
  }

  /**
   * Get historical data for analysis
   */
  async getHistoricalData(
    symbol: string, 
    resolution: string = '1D', 
    fromDate: string, 
    toDate: string
  ): Promise<any> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/history', {
        params: {
          symbol: formattedSymbol,
          resolution,
          date_format: '1',
          range_from: fromDate,
          range_to: toDate,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }

  /**
   * Format symbol for Fyers API
   */
  private formatSymbol(symbol: string): string {
    // Handle index symbols
    if (['NIFTY', 'BANKNIFTY', 'FINNIFTY'].includes(symbol)) {
      return `NSE:${symbol}-INDEX`;
    }
    
    // Handle equity symbols
    return `NSE:${symbol}-EQ`;
  }

  /**
   * Transform Fyers API response to our data structure
   */
  private transformOptionsChainData(data: any, symbol: string): OptionsChainData {
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];

    // Process options chain data
    if (data.optionsChain && Array.isArray(data.optionsChain)) {
      data.optionsChain.forEach((option: any) => {
        const optionData: OptionData = {
          symbol: option.symbol || '',
          strikePrice: option.strike_price || 0,
          optionType: option.option_type === 'CE' ? 'CE' : 'PE',
          expiryDate: option.expiry || '',
          ltp: option.ltp || 0,
          bid: option.bid || 0,
          ask: option.ask || 0,
          volume: option.volume || 0,
          openInterest: option.oi || 0,
          impliedVolatility: option.iv || 0,
          delta: option.delta || 0,
          gamma: option.gamma || 0,
          theta: option.theta || 0,
          vega: option.vega || 0,
          rho: option.rho || 0,
          change: option.ch || 0,
          changePercent: option.chp || 0,
        };

        if (option.option_type === 'CE') {
          callOptions.push(optionData);
        } else {
          putOptions.push(optionData);
        }
      });
    }

    // Sort by strike price
    callOptions.sort((a, b) => a.strikePrice - b.strikePrice);
    putOptions.sort((a, b) => a.strikePrice - b.strikePrice);

    return {
      underlyingSymbol: symbol,
      underlyingPrice: data.underlying?.ltp || 0,
      expiryDate: data.expiry || '',
      callOptions,
      putOptions,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get available expiry dates for a symbol
   */
  async getExpiryDates(symbol: string): Promise<string[]> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/optchain', {
        params: { symbol: formattedSymbol, get_expiry: 1 },
      });

      if (response.data.s !== 'ok' || !response.data.data) {
        return [];
      }

      return response.data.data.expiry_dates || [];
    } catch (error) {
      console.error('Error fetching expiry dates:', error);
      return [];
    }
  }
}

// Export singleton instance
export const fyersService = new FyersService();
export default fyersService;
