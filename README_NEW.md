# Options Chain Analyzer

A professional-grade React application for analyzing options chains with AI-powered strike price recommendations using Fyers API and Google Gemini AI.

## 🚀 Features

- **Real-time Options Data**: Live options chain data from Fyers API
- **AI-Powered Analysis**: Intelligent strike price recommendations using Google Gemini AI
- **Advanced Filtering**: Sort and filter options by various parameters
- **Risk Assessment**: Comprehensive risk analysis for each recommendation
- **Modern UI**: Clean, responsive interface built with Material-UI
- **Real-time Updates**: Auto-refresh capabilities for live market data

## 📋 Prerequisites

Before setting up the application, you need:

1. **Fyers Trading Account**: Active Fyers trading account
2. **Fyers API Access**: API credentials from Fyers
3. **Google AI Studio Account**: For Gemini AI API access
4. **Node.js**: Version 18 or higher

## 🔧 Setup Instructions

### Step 1: Fyers API Setup

1. **Create Fyers API App**:
   - Visit [Fyers API Dashboard](https://myapi.fyers.in/dashboard/)
   - Click "Create New App"
   - Fill in app details:
     - App Name: `Options Chain Analyzer`
     - App Type: `Web App`
     - Redirect URL: `http://localhost:3000/auth/callback`

2. **Get API Credentials**:
   - Note down your `App ID` and `Secret ID`
   - These will be used in the `.env` file

3. **Generate Access Token**:
   - Fyers uses OAuth 2.0 flow
   - You need to generate access token programmatically
   - Access tokens are valid for 24 hours and need daily regeneration

### Step 2: Google Gemini AI Setup

1. **Get API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the API key for use in `.env` file

### Step 3: Environment Configuration

1. **Copy Environment File**:
   ```bash
   cp .env .env.local
   ```

2. **Update `.env.local`** with your credentials:
   ```env
   # Fyers API Configuration
   VITE_FYERS_APP_ID=your_app_id_here
   VITE_FYERS_SECRET_ID=your_secret_id_here
   VITE_FYERS_ACCESS_TOKEN=your_access_token_here

   # Google Gemini AI Configuration
   VITE_GEMINI_API_KEY=your_gemini_api_key_here

   # API Base URLs (Don't change these)
   VITE_FYERS_BASE_URL=https://api-t1.fyers.in/api/v3
   VITE_GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta
   ```

### Step 4: Installation

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start Development Server**:
   ```bash
   npm run dev
   ```

3. **Open Application**:
   - Navigate to `http://localhost:5173`
   - The application should load successfully

## 🔐 Fyers Access Token Generation

Since Fyers access tokens expire daily, you need to implement token generation. Here's a basic flow:

### Manual Token Generation (for development):

1. **Authorization URL**:
   ```
   https://api-t1.fyers.in/api/v3/generate-authcode?client_id=YOUR_APP_ID&redirect_uri=YOUR_REDIRECT_URI&response_type=code&state=sample_state
   ```

2. **Get Auth Code**:
   - Visit the authorization URL
   - Login with your Fyers credentials
   - Copy the auth code from the redirect URL

3. **Generate Access Token**:
   ```javascript
   const response = await fetch('https://api-t1.fyers.in/api/v3/validate-authcode', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
     },
     body: JSON.stringify({
       grant_type: 'authorization_code',
       appIdHash: 'SHA256_HASH_OF_APP_ID:SECRET_ID',
       code: 'AUTH_CODE_FROM_STEP_2'
     })
   });
   ```

### Automated Token Generation (recommended):

For production use, implement automated token generation using:
- Selenium WebDriver for automated login
- Scheduled token refresh (daily)
- Secure token storage

## 📊 Usage

1. **Select Stock**: Choose from popular F&O stocks
2. **Load Data**: Click "Refresh Data" to fetch options chain
3. **AI Analysis**: Click "AI Analysis" for strike recommendations
4. **Review Results**: Analyze recommendations with risk metrics

## 🛠️ Development

### Project Structure
```
src/
├── components/          # React components
├── services/           # API services
├── types/              # TypeScript types
├── utils/              # Utility functions
└── assets/             # Static assets
```

### Key Components
- `Dashboard`: Main application dashboard
- `StockSelector`: Stock selection interface
- `OptionsChain`: Options chain data table
- `AIAnalysis`: AI recommendations display

### API Services
- `fyersService`: Fyers API integration
- `geminiService`: Google Gemini AI integration

## ⚠️ Important Notes

1. **Financial Data**: This application uses real financial data. Use responsibly.
2. **API Limits**: Be aware of API rate limits for both Fyers and Gemini
3. **Token Security**: Never commit access tokens to version control
4. **Market Hours**: Options data is only available during market hours
5. **Risk Disclaimer**: This is for educational purposes. Not financial advice.

## 🔍 Troubleshooting

### Common Issues:

1. **"Invalid access token"**:
   - Regenerate your Fyers access token
   - Ensure token is correctly set in `.env.local`

2. **"No options data available"**:
   - Check if market is open
   - Verify symbol format
   - Ensure API credentials are correct

3. **AI Analysis fails**:
   - Check Gemini API key
   - Verify API quota limits
   - Check network connectivity

### Debug Mode:
Enable debug logging by adding to `.env.local`:
```env
VITE_DEBUG=true
```

## 📝 License

This project is for educational and personal use only. Please ensure compliance with your broker's API terms of service.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues related to:
- **Fyers API**: Contact Fyers support or check their community forum
- **Google Gemini**: Check Google AI documentation
- **Application bugs**: Create an issue in this repository

---

**Disclaimer**: This application is for educational purposes only. Trading in financial markets involves risk. Always do your own research and consult with financial advisors before making investment decisions.