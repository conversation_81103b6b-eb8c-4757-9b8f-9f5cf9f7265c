import React, { useState } from 'react';
import {
  Autocomplete,
  TextField,
  Box,
  Typography,
  Chip,
  Avatar,
} from '@mui/material';
import { TrendingUp, ShowChart } from '@mui/icons-material';
import { StockSymbol } from '../types';
import { FO_STOCKS } from '../utils/constants';

interface StockSelectorProps {
  selectedStock: StockSymbol | null;
  onStockSelect: (stock: StockSymbol) => void;
  disabled?: boolean;
}

const StockSelector: React.FC<StockSelectorProps> = ({
  selectedStock,
  onStockSelect,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState('');

  // Handle stock selection
  const handleStockChange = (_event: React.SyntheticEvent, newValue: StockSymbol | null) => {
    if (newValue) {
      onStockSelect(newValue);
    }
  };

  // Get stock category color
  const getStockCategoryColor = (symbol: string): 'primary' | 'secondary' | 'success' => {
    if (['NIFTY', 'BANKNIFTY', 'FINNIFTY'].includes(symbol)) {
      return 'primary';
    }
    if (['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK'].includes(symbol)) {
      return 'success';
    }
    return 'secondary';
  };

  // Get stock category icon
  const getStockIcon = (symbol: string) => {
    if (['NIFTY', 'BANKNIFTY', 'FINNIFTY'].includes(symbol)) {
      return <ShowChart />;
    }
    return <TrendingUp />;
  };

  return (
    <Box>
      <Autocomplete
        value={selectedStock}
        onChange={handleStockChange}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        options={FO_STOCKS}
        getOptionLabel={(option) => `${option.symbol} - ${option.name}`}
        disabled={disabled}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Select F&O Stock"
            placeholder="Search for stocks..."
            variant="outlined"
            fullWidth
            helperText="Select from popular F&O stocks for options analysis"
          />
        )}
        renderOption={(props, option) => (
          <Box component="li" {...props}>
            <Avatar
              sx={{ 
                mr: 2, 
                bgcolor: `${getStockCategoryColor(option.symbol)}.main`,
                width: 32,
                height: 32,
              }}
            >
              {getStockIcon(option.symbol)}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="body1" fontWeight="medium">
                {option.symbol}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {option.name}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Chip
                label={`Lot: ${option.lotSize}`}
                size="small"
                color={getStockCategoryColor(option.symbol)}
                variant="outlined"
              />
            </Box>
          </Box>
        )}
        filterOptions={(options, { inputValue }) => {
          const filtered = options.filter((option) =>
            option.symbol.toLowerCase().includes(inputValue.toLowerCase()) ||
            option.name.toLowerCase().includes(inputValue.toLowerCase())
          );
          
          // Sort by relevance: exact matches first, then partial matches
          return filtered.sort((a, b) => {
            const aSymbolMatch = a.symbol.toLowerCase().startsWith(inputValue.toLowerCase());
            const bSymbolMatch = b.symbol.toLowerCase().startsWith(inputValue.toLowerCase());
            
            if (aSymbolMatch && !bSymbolMatch) return -1;
            if (!aSymbolMatch && bSymbolMatch) return 1;
            
            return a.symbol.localeCompare(b.symbol);
          });
        }}
        isOptionEqualToValue={(option, value) => option.symbol === value.symbol}
        sx={{ mb: 2 }}
      />

      {/* Selected Stock Info */}
      {selectedStock && (
        <Box
          sx={{
            p: 2,
            border: 1,
            borderColor: 'divider',
            borderRadius: 2,
            bgcolor: 'background.paper',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Avatar
              sx={{ 
                mr: 2, 
                bgcolor: `${getStockCategoryColor(selectedStock.symbol)}.main`,
              }}
            >
              {getStockIcon(selectedStock.symbol)}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" fontWeight="bold">
                {selectedStock.symbol}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedStock.name}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              icon={<TrendingUp />}
              label={`Lot Size: ${selectedStock.lotSize}`}
              color="primary"
              variant="outlined"
              size="small"
            />
            <Chip
              label={`Tick Size: ₹${selectedStock.tickSize}`}
              color="secondary"
              variant="outlined"
              size="small"
            />
            {['NIFTY', 'BANKNIFTY', 'FINNIFTY'].includes(selectedStock.symbol) && (
              <Chip
                label="Index"
                color="info"
                variant="filled"
                size="small"
              />
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default StockSelector;
