import { OptionsChainData, OptionData, AIAnalysisResponse, StrikeRecommendation } from '../types';

/**
 * Mock data service for development and testing
 * This provides realistic options chain data when real APIs are not available
 */
class MockDataService {
  /**
   * Generate mock options chain data
   */
  generateMockOptionsChain(symbol: string, spotPrice: number = 23500): OptionsChainData {
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];
    
    // Generate strikes around spot price
    const baseStrike = Math.floor(spotPrice / 100) * 100;
    const strikes = [];
    
    // Generate strikes from -1000 to +1000 around spot
    for (let i = -10; i <= 10; i++) {
      strikes.push(baseStrike + (i * 100));
    }
    
    strikes.forEach(strike => {
      // Calculate basic Greeks and pricing
      const isITM_Call = spotPrice > strike;
      const isITM_Put = spotPrice < strike;
      const moneyness = Math.abs(spotPrice - strike) / spotPrice;
      
      // Mock Call Option
      const callLTP = isITM_Call ? 
        Math.max(spotPrice - strike + Math.random() * 50, 0.05) :
        Math.max(Math.random() * 100 * Math.exp(-moneyness * 5), 0.05);
      
      const callOption: OptionData = {
        symbol: `${symbol}${this.formatDate()}${strike}CE`,
        strikePrice: strike,
        optionType: 'CE',
        expiryDate: this.getNextThursday(),
        ltp: Math.round(callLTP * 100) / 100,
        bid: Math.round((callLTP * 0.98) * 100) / 100,
        ask: Math.round((callLTP * 1.02) * 100) / 100,
        volume: Math.floor(Math.random() * 100000),
        openInterest: Math.floor(Math.random() * 500000),
        impliedVolatility: Math.round((15 + Math.random() * 20) * 100) / 100,
        delta: Math.round((isITM_Call ? 0.5 + Math.random() * 0.4 : Math.random() * 0.5) * 1000) / 1000,
        gamma: Math.round(Math.random() * 0.1 * 10000) / 10000,
        theta: -Math.round(Math.random() * 0.5 * 1000) / 1000,
        vega: Math.round(Math.random() * 0.3 * 1000) / 1000,
        rho: Math.round(Math.random() * 0.1 * 1000) / 1000,
        change: Math.round((Math.random() - 0.5) * 20 * 100) / 100,
        changePercent: Math.round((Math.random() - 0.5) * 10 * 100) / 100,
      };
      
      // Mock Put Option
      const putLTP = isITM_Put ? 
        Math.max(strike - spotPrice + Math.random() * 50, 0.05) :
        Math.max(Math.random() * 100 * Math.exp(-moneyness * 5), 0.05);
      
      const putOption: OptionData = {
        symbol: `${symbol}${this.formatDate()}${strike}PE`,
        strikePrice: strike,
        optionType: 'PE',
        expiryDate: this.getNextThursday(),
        ltp: Math.round(putLTP * 100) / 100,
        bid: Math.round((putLTP * 0.98) * 100) / 100,
        ask: Math.round((putLTP * 1.02) * 100) / 100,
        volume: Math.floor(Math.random() * 100000),
        openInterest: Math.floor(Math.random() * 500000),
        impliedVolatility: Math.round((15 + Math.random() * 20) * 100) / 100,
        delta: -Math.round((isITM_Put ? 0.5 + Math.random() * 0.4 : Math.random() * 0.5) * 1000) / 1000,
        gamma: Math.round(Math.random() * 0.1 * 10000) / 10000,
        theta: -Math.round(Math.random() * 0.5 * 1000) / 1000,
        vega: Math.round(Math.random() * 0.3 * 1000) / 1000,
        rho: -Math.round(Math.random() * 0.1 * 1000) / 1000,
        change: Math.round((Math.random() - 0.5) * 20 * 100) / 100,
        changePercent: Math.round((Math.random() - 0.5) * 10 * 100) / 100,
      };
      
      callOptions.push(callOption);
      putOptions.push(putOption);
    });
    
    return {
      underlyingSymbol: symbol,
      underlyingPrice: spotPrice,
      expiryDate: this.getNextThursday(),
      callOptions: callOptions.sort((a, b) => a.strikePrice - b.strikePrice),
      putOptions: putOptions.sort((a, b) => a.strikePrice - b.strikePrice),
      timestamp: new Date().toISOString(),
    };
  }
  
  /**
   * Generate mock AI analysis
   */
  generateMockAIAnalysis(optionsData: OptionsChainData): AIAnalysisResponse {
    const spotPrice = optionsData.underlyingPrice;
    const atmStrike = Math.round(spotPrice / 100) * 100;
    
    // Generate 2-3 recommendations
    const recommendations: StrikeRecommendation[] = [
      {
        strikePrice: atmStrike,
        optionType: 'CE',
        confidence: 85,
        reasoning: `Strong bullish momentum detected. The ${atmStrike} CE shows high volume surge with increasing open interest. Delta of 0.52 provides good directional exposure while maintaining reasonable time decay. IV is below 30th percentile, making it attractive for buying.`,
        entryPrice: 150.50,
        targetPrice: 225.00,
        stopLoss: 100.00,
        riskReward: 1.5,
        maxLoss: 50.50,
        maxProfit: 74.50,
        breakeven: atmStrike + 150.50,
      },
      {
        strikePrice: atmStrike + 200,
        optionType: 'CE',
        confidence: 72,
        reasoning: `OTM call with attractive risk-reward profile. High gamma provides acceleration potential on upward moves. Volume is 3x average with fresh OI buildup indicating institutional interest.`,
        entryPrice: 45.25,
        targetPrice: 85.00,
        stopLoss: 25.00,
        riskReward: 2.0,
        maxLoss: 20.25,
        maxProfit: 39.75,
        breakeven: atmStrike + 200 + 45.25,
      },
    ];
    
    return {
      recommendations,
      marketOutlook: this.getRandomMarketOutlook(),
      riskAssessment: `Current market conditions show moderate volatility with ${optionsData.underlyingSymbol} trading near key support/resistance levels. Risk is elevated due to upcoming events. Position sizing should be conservative with strict stop losses.`,
      keyInsights: [
        'High volume activity in ATM and OTM calls suggests bullish sentiment',
        'Put-Call ratio indicates balanced positioning with slight call bias',
        'IV percentile is in favorable range for option buying strategies',
        'Strong support visible at lower strikes based on OI distribution',
      ],
      warnings: [
        'Theta decay will accelerate as expiry approaches',
        'High volatility environment may lead to rapid price swings',
        'Monitor global market cues for sudden sentiment changes',
      ],
      timestamp: new Date().toISOString(),
    };
  }
  
  /**
   * Simulate API delay
   */
  async delay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  private formatDate(): string {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}${month}${day}`;
  }
  
  private getNextThursday(): string {
    const today = new Date();
    const daysUntilThursday = (4 - today.getDay() + 7) % 7;
    const nextThursday = new Date(today);
    nextThursday.setDate(today.getDate() + (daysUntilThursday || 7));
    return nextThursday.toISOString().split('T')[0];
  }
  
  private getRandomMarketOutlook(): string {
    const outlooks = [
      'Bullish - Strong upward momentum with increasing volume and positive market sentiment',
      'Bearish - Downward pressure with high volatility and risk-off sentiment prevailing',
      'Neutral - Sideways consolidation expected with range-bound trading likely',
      'Volatile - High uncertainty with potential for sharp moves in either direction',
    ];
    return outlooks[Math.floor(Math.random() * outlooks.length)];
  }
}

export const mockDataService = new MockDataService();
export default mockDataService;
