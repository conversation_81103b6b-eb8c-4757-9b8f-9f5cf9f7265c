# Complete Setup Guide - Options Chain Analyzer

## 🎯 Quick Start Checklist

- [ ] Fyers trading account created
- [ ] Fyers API app registered
- [ ] Google Gemini API key obtained
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Access token generated
- [ ] Application tested

## 📋 Detailed Setup Process

### 1. Prerequisites Setup

#### A. Fyers Trading Account
1. Open a Fyers trading account at [fyers.in](https://fyers.in)
2. Complete KYC and account activation
3. Ensure you have trading permissions for F&O segment

#### B. System Requirements
- Node.js 18+ installed
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Stable internet connection
- Text editor (VS Code recommended)

### 2. Fyers API Configuration

#### Step 1: Create API App
1. Login to [Fyers API Dashboard](https://myapi.fyers.in/dashboard/)
2. Click "Create New App"
3. Fill the form:
   ```
   App Name: Options Chain Analyzer
   App Type: Web App
   Redirect URL: http://localhost:3000/auth/callback
   Description: Options chain analysis with AI recommendations
   ```
4. Submit and note down:
   - **App ID** (format: XXXXXXXXXX-100)
   - **Secret ID** (format: XXXXXXXXXX)

#### Step 2: API Permissions
Ensure your app has the following permissions:
- [x] Read access to market data
- [x] Read access to options chain
- [x] Read access to quotes

### 3. Google Gemini AI Setup

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key
5. Keep it secure - this will be used in environment variables

### 4. Project Setup

#### Step 1: Clone and Install
```bash
# Clone the repository
git clone <repository-url>
cd options-chain-analyzer

# Install dependencies
npm install
```

#### Step 2: Environment Configuration
```bash
# Copy environment template
cp .env .env.local

# Edit .env.local with your credentials
nano .env.local
```

#### Step 3: Configure Environment Variables
Update `.env.local` with your actual values:

```env
# Fyers API Configuration
VITE_FYERS_APP_ID=YOUR_APP_ID_HERE
VITE_FYERS_SECRET_ID=YOUR_SECRET_ID_HERE
VITE_FYERS_ACCESS_TOKEN=YOUR_ACCESS_TOKEN_HERE

# Google Gemini AI Configuration
VITE_GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE

# API Base URLs (Don't change)
VITE_FYERS_BASE_URL=https://api-t1.fyers.in/api/v3
VITE_GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta
```

### 5. Access Token Generation

#### Method 1: Using the Built-in Script (Recommended)
```bash
# Run the token generation script
npm run generate-token

# Follow the prompts:
# 1. Visit the authorization URL
# 2. Login to Fyers
# 3. Copy the auth code from redirect URL
# 4. Paste it in the terminal
# 5. Copy the generated access token to .env.local
```

#### Method 2: Manual Process
1. **Generate Authorization URL**:
   ```
   https://api-t1.fyers.in/api/v3/generate-authcode?client_id=YOUR_APP_ID&redirect_uri=http://localhost:3000/auth/callback&response_type=code&state=sample_state
   ```

2. **Get Auth Code**:
   - Visit the URL above
   - Login with Fyers credentials
   - You'll be redirected to: `http://localhost:3000/auth/callback?auth_code=XXXXXX&state=sample_state`
   - Copy the `auth_code` value

3. **Generate Access Token**:
   Use a tool like Postman or curl:
   ```bash
   curl -X POST https://api-t1.fyers.in/api/v3/validate-authcode \
     -H "Content-Type: application/json" \
     -d '{
       "grant_type": "authorization_code",
       "appIdHash": "SHA256_HASH_OF_APPID:SECRETID",
       "code": "YOUR_AUTH_CODE"
     }'
   ```

### 6. Testing the Setup

#### Step 1: Start Development Server
```bash
npm run dev
```

#### Step 2: Open Application
Navigate to `http://localhost:5173`

#### Step 3: Test Basic Functionality
1. **Configuration Check**: Look for any red error messages about missing configuration
2. **Stock Selection**: Try selecting a stock (e.g., NIFTY, RELIANCE)
3. **Data Loading**: Click "Refresh Data" - should load options chain
4. **AI Analysis**: Click "AI Analysis" - should provide recommendations

### 7. Troubleshooting Common Issues

#### Issue: "Invalid access token"
**Solution**:
- Regenerate access token using the script
- Ensure token is correctly copied to .env.local
- Check for extra spaces or characters

#### Issue: "No options data available"
**Possible Causes**:
- Market is closed
- Invalid symbol format
- API rate limits exceeded
- Network connectivity issues

**Solutions**:
- Check if market is open (9:15 AM - 3:30 PM IST, Mon-Fri)
- Try with different stock symbols
- Wait a few minutes and retry

#### Issue: "AI analysis failed"
**Possible Causes**:
- Invalid Gemini API key
- API quota exceeded
- Network issues

**Solutions**:
- Verify Gemini API key
- Check API usage limits in Google AI Studio
- Try again after some time

#### Issue: Configuration errors on startup
**Solution**:
- Check all environment variables are set
- Ensure no typos in variable names
- Restart development server after changes

### 8. Production Deployment

#### Environment Variables for Production
```env
# Use production Fyers API URL
VITE_FYERS_BASE_URL=https://api.fyers.in/api/v3

# Ensure secure token management
# Consider using environment-specific secrets
```

#### Security Considerations
- Never commit `.env.local` to version control
- Use secure token storage in production
- Implement token refresh automation
- Set up monitoring for API failures

### 9. Daily Operations

#### Token Refresh (Required Daily)
```bash
# Run this every day before market hours
npm run generate-token
```

#### Monitoring
- Check application logs for errors
- Monitor API usage limits
- Verify data accuracy during market hours

### 10. Support and Resources

#### Official Documentation
- [Fyers API Docs](https://myapi.fyers.in/docs/)
- [Google Gemini AI Docs](https://ai.google.dev/docs)

#### Community Support
- [Fyers Community Forum](https://fyers.in/community/)
- [GitHub Issues](link-to-your-repo/issues)

#### Contact Information
- For API issues: Contact Fyers support
- For application bugs: Create GitHub issue
- For feature requests: Submit GitHub issue

---

## ✅ Verification Checklist

After completing setup, verify:

- [ ] Application loads without configuration errors
- [ ] Can select stocks from dropdown
- [ ] "Refresh Data" loads options chain successfully
- [ ] Options data displays in table format
- [ ] "AI Analysis" generates recommendations
- [ ] No console errors in browser developer tools
- [ ] All environment variables are properly set
- [ ] Access token is valid and working

## 🚨 Important Reminders

1. **Daily Token Refresh**: Fyers access tokens expire every 24 hours
2. **Market Hours**: Options data is only available during trading hours
3. **API Limits**: Be mindful of rate limits for both APIs
4. **Security**: Keep your API keys secure and never share them
5. **Compliance**: Ensure usage complies with your broker's terms of service

---

**Need Help?** If you encounter issues not covered in this guide, please create a GitHub issue with:
- Error messages (if any)
- Steps to reproduce the problem
- Your environment details (OS, Node.js version, etc.)
- Screenshots (if applicable)
