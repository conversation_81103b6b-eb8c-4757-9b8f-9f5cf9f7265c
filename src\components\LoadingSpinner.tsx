import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Card,
  CardContent,
} from '@mui/material';

interface LoadingSpinnerProps {
  message?: string;
  size?: number;
  fullHeight?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 40,
  fullHeight = false,
}) => {
  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: fullHeight ? 10 : 4,
            textAlign: 'center',
          }}
        >
          <CircularProgress size={size} sx={{ mb: 2 }} />
          <Typography variant="body1" color="text.secondary">
            {message}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default LoadingSpinner;
